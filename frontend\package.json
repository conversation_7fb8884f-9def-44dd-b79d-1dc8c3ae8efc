{"name": "pos-frontend", "version": "1.0.0", "type": "module", "main": "./out/main/index.js", "scripts": {"dev": "electron-vite dev", "build": "electron-vite build", "preview": "electron-vite preview"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^3.0.0", "@fontsource/inter": "^5.2.6", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-form": "^0.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.83.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.60.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@types/react": "^18.3.14", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "electron": "^33.2.1", "electron-vite": "^2.3.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.1", "typescript": "^5.7.2", "vite": "^5.4.10"}}