/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./src/renderer/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    screens: {
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
      'kiosk': '1920px',
      'electron-sm': '800px',
      'electron-md': '1024px',
      'electron-lg': '1280px',
      'electron-xl': '1440px',
    },
    extend: {
      colors: {
        // 🎯 KIOSK GLOBAL COLOR SYSTEM - Tek renk sistemi
        kiosk: {
          bg: '#f9fafb',        // page background
          card: '#ffffff',      // card / modal background
          input: '#f3f4f6',     // input background
          border: '#e5e7eb',    // border color
          text: '#111827',      // primary text
          muted: '#6b7280',     // secondary text
          primary: '#3b82f6',   // accent blue
          'primary-light': '#F0F8FF',  // açık mavi hover
          error: '#ef4444',     // error red
          success: '#22c55e',   // success green
          warning: '#f59e0b',   // warning orange
        },
      },
      keyframes: {
        slideIn: {
          from: { transform: 'translateX(calc(100% + 1rem))' },
          to: { transform: 'translateX(0)' },
        },
        hide: {
          from: { opacity: '1' },
          to: { opacity: '0' },
        },
        swipeOut: {
          from: { transform: 'translateX(var(--radix-toast-swipe-end-x))' },
          to: { transform: 'translateX(calc(100% + 1rem))' },
        },
      },
      animation: {
        slideIn: 'slideIn 150ms cubic-bezier(0.16, 1, 0.3, 1)',
        hide: 'hide 100ms ease-in',
        swipeOut: 'swipeOut 100ms ease-out',
      },
    },
  },
  plugins: [],
}
