import React from 'react';
import { Delete } from 'lucide-react';
import { Button } from '../common/Button';
import { cn } from '../../lib/utils';

interface NumericKeypadProps {
  onNumberClick: (number: string) => void;
  onDeleteClick: () => void;
  disabled?: boolean;
  className?: string;
}

export const NumericKeypad: React.FC<NumericKeypadProps> = ({
  onNumberClick,
  onDeleteClick,
  disabled = false,
  className
}) => {
  const numbers = [
    ['1', '2', '3'],
    ['4', '5', '6'],
    ['7', '8', '9'],
    ['', '0', 'delete']
  ];

  return (
    <div className={cn('grid grid-cols-3 gap-3 max-w-xs mx-auto', className)}>
      {numbers.flat().map((item, index) => {
        if (item === '') {
          return <div key={index} />; // Empty space
        }
        
        if (item === 'delete') {
          return (
            <Button
              key={index}
              variant="pin"
              size="pin"
              onClick={onDeleteClick}
              disabled={disabled}
              className="flex items-center justify-center hover:bg-red-50 hover:border-kiosk-error"
            >
              <Delete className="w-5 h-5 text-kiosk-text" />
            </Button>
          );
        }

        return (
          <Button
            key={index}
            variant="pin"
            size="pin"
            onClick={() => onNumberClick(item)}
            disabled={disabled}
          >
            {item}
          </Button>
        );
      })}
    </div>
  );
};
