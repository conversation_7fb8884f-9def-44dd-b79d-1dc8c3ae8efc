import React from 'react';
import {
  Plus,
  Package,
  ChevronLeft,
  ChevronRight,
  RotateCcw,
  FolderPlus
} from 'lucide-react';
import { Button } from '../components/common/Button';
import { ProductList } from '../components/features/ProductList';
import { ProductForm } from '../components/features/ProductForm';
import { CategoryForm } from '../components/features/CategoryForm';
import { ProductSearch, ProductFilters } from '../components/features/ProductSearch';
import { DeleteConfirmationDialog } from '../components/features/DeleteConfirmationDialog';
import { useProductStore } from '../store/productStore';
import { cn } from '../lib/utils';

interface ProductManagementProps {
  className?: string;
}

export const ProductManagement: React.FC<ProductManagementProps> = ({ className }) => {
  const {
    productPagination,
    loading,
    error,
    isProductModalOpen,
    isCategoryModalOpen,
    isDeleteDialogOpen,
    setProductPagination,
    openProductModal,
    closeProductModal,
    openCategoryModal,
    closeCategoryModal,
    closeDeleteDialog,
    clearError
  } = useProductStore();

  const handlePageChange = (page: number) => {
    setProductPagination({ page });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleRefresh = () => {
    // This will trigger a refetch through React Query
    clearError();
    window.location.reload();
  };

  const renderPagination = () => {
    if (productPagination.totalPages <= 1) return null;

    const { page, totalPages } = productPagination;
    const pages = [];
    
    // Calculate page range to show
    const maxVisiblePages = 5;
    let startPage = Math.max(1, page - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return (
      <div className="flex items-center justify-center gap-2 mt-8">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(page - 1)}
          disabled={page === 1}
          className="h-8 w-8 p-0"
        >
          <ChevronLeft className="w-4 h-4" />
        </Button>

        {startPage > 1 && (
          <>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(1)}
              className="h-8 w-8 p-0"
            >
              1
            </Button>
            {startPage > 2 && (
              <span className="text-kiosk-muted text-sm">...</span>
            )}
          </>
        )}

        {pages.map((pageNum) => (
          <Button
            key={pageNum}
            variant={pageNum === page ? "default" : "outline"}
            size="sm"
            onClick={() => handlePageChange(pageNum)}
            className="h-8 w-8 p-0"
          >
            {pageNum}
          </Button>
        ))}

        {endPage < totalPages && (
          <>
            {endPage < totalPages - 1 && (
              <span className="text-kiosk-muted text-sm">...</span>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(totalPages)}
              className="h-8 w-8 p-0"
            >
              {totalPages}
            </Button>
          </>
        )}

        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(page + 1)}
          disabled={page === totalPages}
          className="h-8 w-8 p-0"
        >
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    );
  };

  return (
    <div className={cn("kiosk bg-kiosk-bg flex flex-col", className)}>
      {/* Optional: hide Electron title-bar */}
      <style>{`
        .electron-titlebar {
          display: none;
        }
      `}</style>

      {/* Header with action buttons - compact for kiosk */}
      <div className="bg-kiosk-card border-b border-kiosk-border px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Package className="w-5 h-5 text-kiosk-primary" />
            </div>
            <div>
              <h1 className="text-lg font-semibold text-kiosk-text">
                Ürün Yönetimi
              </h1>
              <p className="text-xs text-kiosk-muted">
                Ürünleri görüntüleyin, ekleyin ve düzenleyin
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading.products}
              className="h-8"
            >
              <RotateCcw className={cn(
                "w-3 h-3 mr-1",
                loading.products && "animate-spin"
              )} />
              Yenile
            </Button>

            <Button
              variant="outline"
              onClick={() => openCategoryModal('create')}
              className="h-8"
            >
              <FolderPlus className="w-3 h-3 mr-1" />
              Yeni Kategori
            </Button>

            <Button
              onClick={() => openProductModal('create')}
              className="h-8"
            >
              <Plus className="w-3 h-3 mr-1" />
              Yeni Ürün
            </Button>
          </div>
        </div>
      </div>

      {/* Main grid (no header) */}
      <div className="flex-1 grid grid-cols-1 lg:grid-cols-4 gap-0 overflow-hidden">
        <aside className="lg:col-span-1 bg-kiosk-card border-r border-kiosk-border p-4 overflow-auto">
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-kiosk-text mb-3">Filtreler</h3>
            <ProductFilters />
          </div>
        </aside>

        <main className="lg:col-span-3 p-4 overflow-auto bg-kiosk-bg">
          <div className="space-y-4">
            {/* Search Bar */}
            <div className="bg-kiosk-card rounded-lg p-3 border border-kiosk-border">
              <ProductSearch />
            </div>

            {/* Results Summary */}
            {productPagination.total > 0 && (
              <div className="flex items-center justify-between text-xs text-kiosk-muted">
                <span>
                  {productPagination.total} ürün bulundu
                </span>
                <span>
                  Sayfa {productPagination.page} / {productPagination.totalPages}
                </span>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <div className="bg-red-50 border border-kiosk-error/20 rounded-lg p-3">
                <div className="flex items-center gap-2 text-kiosk-error">
                  <Package className="w-4 h-4" />
                  <span className="text-xs font-medium">Hata:</span>
                  <span className="text-xs">{error.message}</span>
                </div>
              </div>
            )}

            {/* Product List */}
            <ProductList />

            {/* Pagination */}
            {renderPagination()}
          </div>
        </main>
      </div>

      {/* Modals */}
      <ProductForm
        open={isProductModalOpen}
        onClose={closeProductModal}
      />

      <CategoryForm
        open={isCategoryModalOpen}
        onClose={closeCategoryModal}
      />

      <DeleteConfirmationDialog
        open={isDeleteDialogOpen}
        onClose={closeDeleteDialog}
      />
    </div>
  );
};
