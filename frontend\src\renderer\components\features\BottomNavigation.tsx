// BottomNavigation.tsx
// Alt navigasyon barı component'i

import React, { useState } from 'react';
import {
  Home,
  ShoppingCart,
  Users,
  MoreHorizontal,
  Plus
} from 'lucide-react';

import { MoreModal } from './MoreModal';
import { cn } from '../../lib/utils';

export interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  isActive?: boolean;
  onClick?: () => void;
}

export interface BottomNavigationProps {
  activeTab?: string;
  onTabChange?: (tabId: string) => void;
  onCreateOrder?: () => void;
  onNavigate?: (page: string) => void;
}

export const BottomNavigation: React.FC<BottomNavigationProps> = ({
  activeTab = 'home',
  onTabChange,
  onCreateOrder,
  onNavigate
}) => {
  const [isMoreModalOpen, setIsMoreModalOpen] = useState(false);

  const navigationItems: NavigationItem[] = [
    {
      id: 'home',
      label: '<PERSON> Say<PERSON>',
      icon: <Home className="w-5 h-5" />,
      isActive: activeTab === 'home',
      onClick: () => onTabChange?.('home')
    },
    {
      id: 'orders',
      label: 'Siparişler',
      icon: <ShoppingCart className="w-5 h-5" />,
      isActive: activeTab === 'orders',
      onClick: () => onTabChange?.('orders')
    },
    {
      id: 'tables',
      label: 'Masalar',
      icon: <Users className="w-5 h-5" />,
      isActive: activeTab === 'tables',
      onClick: () => onTabChange?.('tables')
    },
    {
      id: 'more',
      label: 'Daha Fazla',
      icon: <MoreHorizontal className="w-5 h-5" />,
      isActive: false,
      onClick: () => setIsMoreModalOpen(true)
    }
  ];

  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 bg-kiosk-card border-t border-kiosk-border shadow-lg z-50">
        <div className="flex items-center px-4 py-3 max-w-full">
          {navigationItems.map((item, index) => (
            <React.Fragment key={item.id}>
              {/* Navigation Item */}
              <button
                onClick={item.onClick}
                className={cn(
                  'flex items-center justify-center py-2 px-3 rounded-lg transition-all duration-200 flex-1 min-w-0',
                  item.isActive
                    ? 'text-kiosk-primary bg-kiosk-primary-light'
                    : 'text-kiosk-muted hover:text-kiosk-primary hover:bg-kiosk-primary-light'
                )}
              >
                <div className={cn(
                  'transition-transform duration-200 mr-2 flex-shrink-0',
                  item.isActive && 'scale-110'
                )}>
                  {item.icon}
                </div>
                <span className="text-sm font-medium truncate">
                  {item.label}
                </span>
                {item.isActive && (
                  <div className="w-1 h-1 bg-kiosk-primary-light rounded-full ml-2 flex-shrink-0" />
                )}
              </button>

              {/* Floating Action Button - Yeni Sipariş */}
              {index === 1 && (
                <div className="absolute left-1/2 transform -translate-x-1/2 -top-8 z-10">
                  <button
                    onClick={onCreateOrder}
                    className="w-16 h-16 bg-kiosk-primary-light hover:bg-kiosk-primary-light rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center border-4 border-white"
                  >
                    <Plus className="w-8 h-8 text-white" />
                  </button>
                </div>
              )}
            </React.Fragment>
          ))}
        </div>
      </div>

      {/* More Modal */}
      <MoreModal
        isOpen={isMoreModalOpen}
        onClose={() => setIsMoreModalOpen(false)}
        onNavigate={onNavigate}
      />
    </>
  );
};
