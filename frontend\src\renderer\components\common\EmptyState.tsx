// EmptyState.tsx
// Boş durum gösterimi için component

import React from 'react';
import { Button } from './Button';
import { cn } from '../../lib/utils';

export interface EmptyStateProps {
  icon?: React.ReactNode;
  title: string;
  description?: string;
  actionLabel?: string;
  onAction?: () => void;
  className?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  description,
  actionLabel,
  onAction,
  className
}) => {
  return (
    <div className={cn(
      'flex flex-col items-center justify-center py-12 px-4 text-center',
      className
    )}>
      {/* Icon */}
      {icon && (
        <div className="w-16 h-16 mb-4 text-kiosk-text flex items-center justify-center">
          {icon}
        </div>
      )}

      {/* Title */}
      <h3 className="text-lg font-medium text-kiosk-text mb-2">
        {title}
      </h3>

      {/* Description */}
      {description && (
        <p className="text-sm text-kiosk-text mb-6 max-w-sm">
          {description}
        </p>
      )}

      {/* Action Button */}
      {actionLabel && onAction && (
        <Button
          variant="primary"
          onClick={onAction}
          className="px-6"
        >
          {actionLabel}
        </Button>
      )}
    </div>
  );
};
