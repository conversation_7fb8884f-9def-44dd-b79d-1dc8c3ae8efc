// OutOfStockCard.tsx
// Stokta olmayan ürünler için card component

import React from 'react';
import { Card, CardContent } from '../common/Card';
import { OutOfStockItem } from '../../types/dashboard.types';
import { cn } from '../../lib/utils';
import { Clock } from 'lucide-react';

export interface OutOfStockCardProps {
  item: OutOfStockItem;
  className?: string;
}

export const OutOfStockCard: React.FC<OutOfStockCardProps> = ({
  item,
  className
}) => {
  return (
    <Card className={cn('backdrop-blur-sm border-0 shadow-md hover:shadow-xl transition-all duration-300', className)}>
      <CardContent className="p-3">
        <div className="flex items-center space-x-3">
          {/* Item Image */}
          <div className="flex-shrink-0">
            <div className="relative">
              <img
                src={item.image}
                alt={item.name}
                className="w-10 h-10 rounded-lg object-cover opacity-50 shadow-sm"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/images/default-dish.jpg';
                }}
              />
              {/* Out of stock overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-red-500/30 to-transparent rounded-lg flex items-center justify-center">
                <div className="w-6 h-6 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-md shadow-red-200/50">
                  <span className="text-white text-xs font-bold">!</span>
                </div>
              </div>
            </div>
          </div>

          {/* Item Info */}
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-semibold text-kiosk-text truncate">
              {item.name}
            </h4>
            <div className="flex items-center text-xs text-kiosk-text font-medium">
              <div className="w-5 h-5 bg-slate-100 rounded-md flex items-center justify-center mr-1.5">
                <Clock className="w-3 h-3 text-kiosk-text" />
              </div>
              <span>Tahmini: {item.availableAt}</span>
            </div>
          </div>

          {/* Status Badge */}
          <div className="flex-shrink-0">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-bold bg-gradient-to-r from-red-500 to-red-600 text-white shadow-md shadow-red-200/50">
              Stokta Yok
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
