import React from 'react';
import { cn } from '../../lib/utils';

interface PinInputProps {
  pin: string;
  maxLength?: number;
  className?: string;
  error?: boolean;
}

export const PinInput: React.FC<PinInputProps> = ({
  pin,
  maxLength = 6,
  className,
  error = false
}) => {
  return (
    <div className={cn('flex justify-center space-x-3', className)}>
      {Array.from({ length: maxLength }).map((_, index) => (
        <div
          key={index}
          className={cn(
            'w-12 h-12 rounded-lg border-2 flex items-center justify-center',
            'text-xl font-bold transition-all duration-200',
            pin.length > index
              ? error
                ? 'border-red-500 bg-red-50 text-kiosk-text'
                : 'border-blue-500 bg-blue-50 text-kiosk-text'
              : pin.length === index
              ? 'border-blue-500 bg-blue-50 animate-pulse'
              : 'border-kiosk-border bg-gray-50 text-kiosk-text'
          )}
        >
          {pin.length > index ? '●' : ''}
        </div>
      ))}
    </div>
  );
};
