import React from 'react';
import { cn } from '../../lib/utils';

interface PinInputProps {
  pin: string;
  maxLength?: number;
  className?: string;
  error?: boolean;
}

export const PinInput: React.FC<PinInputProps> = ({
  pin,
  maxLength = 6,
  className,
  error = false
}) => {
  return (
    <div className={cn('flex justify-center space-x-3', className)}>
      {Array.from({ length: maxLength }).map((_, index) => (
        <div
          key={index}
          className={cn(
            'w-12 h-12 rounded-lg border-2 flex items-center justify-center',
            'text-xl font-bold transition-all duration-200',
            pin.length > index
              ? error
                ? 'border-red-500 bg-red-50 text-kiosk-text'
                : 'border-kiosk-primary bg-kiosk-primary-light text-kiosk-text'
              : pin.length === index
              ? 'border-kiosk-primary bg-kiosk-primary-light animate-pulse'
              : 'border-kiosk-border bg-kiosk-input text-kiosk-text'
          )}
        >
          {pin.length > index ? '●' : ''}
        </div>
      ))}
    </div>
  );
};
