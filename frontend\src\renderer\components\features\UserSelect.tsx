import React from 'react';
import * as Select from '@radix-ui/react-select';
import { ChevronDown, User } from 'lucide-react';
import { AvailableShiftUser } from '../../types/auth.types';
import { cn } from '../../lib/utils';

interface UserSelectProps {
  users: AvailableShiftUser[];
  selectedUserId: string | null;
  onUserSelect: (userId: string) => void;
  disabled?: boolean;
}

export const UserSelect: React.FC<UserSelectProps> = ({
  users,
  selectedUserId,
  onUserSelect,
  disabled = false
}) => {
  const selectedUser = users.find(user => user.id === selectedUserId);

  return (
    <Select.Root value={selectedUserId || ''} onValueChange={onUserSelect} disabled={disabled}>
      <Select.Trigger
        className={cn(
          'flex items-center justify-between w-full px-4 py-3 bg-kiosk-input border-2 border-kiosk-border rounded-lg',
          'hover:border-kiosk-primary focus:border-kiosk-primary focus:outline-none transition-colors duration-200',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          selectedUserId ? 'text-kiosk-text' : 'text-kiosk-text'
        )}
      >
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-kiosk-primary-light rounded-full flex items-center justify-center">
            <User className="w-5 h-5 text-kiosk-primary" />
          </div>
          <div className="text-left">
            {selectedUser ? (
              <>
                <div className="font-semibold text-kiosk-text">
                  {selectedUser.firstName} {selectedUser.lastName}
                </div>
                <div className="text-sm text-kiosk-text">
                  {selectedUser.role} • {selectedUser.shiftDetails}
                </div>
              </>
            ) : (
              <div className="font-medium text-kiosk-text">
                Çalışan seçiniz
              </div>
            )}
          </div>
        </div>
        <Select.Icon>
          <ChevronDown className="w-5 h-5 text-kiosk-text" />
        </Select.Icon>
      </Select.Trigger>

      <Select.Portal>
        <Select.Content
          className="bg-kiosk-input border border-kiosk-border rounded-lg shadow-lg z-50 max-h-80 overflow-hidden"
          position="popper"
          sideOffset={8}
        >
          <Select.Viewport className="p-2">
            {users.map((user) => (
              <Select.Item
                key={user.id}
                value={user.id}
                className={cn(
                  'flex items-center space-x-3 px-3 py-3 rounded-md cursor-pointer',
                  'hover:bg-kiosk-primary-light focus:bg-kiosk-primary-light focus:outline-none',
                  'data-[highlighted]:bg-kiosk-primary-light'
                )}
              >
                <div className="w-10 h-10 bg-kiosk-primary-light rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-kiosk-primary" />
                </div>
                <div className="flex-1">
                  <div className="font-semibold text-kiosk-text">
                    {user.firstName} {user.lastName}
                  </div>
                  <div className="text-sm text-kiosk-text">
                    {user.role} • {user.shiftDetails}
                  </div>
                </div>
                <Select.ItemIndicator>
                  <div className="w-2 h-2 bg-kiosk-primary-light rounded-full" />
                </Select.ItemIndicator>
              </Select.Item>
            ))}
          </Select.Viewport>
        </Select.Content>
      </Select.Portal>
    </Select.Root>
  );
};
