import React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-kiosk-primary disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'bg-kiosk-primary text-white hover:opacity-90',
        destructive: 'bg-kiosk-error text-white hover:opacity-90',
        outline: 'border border-kiosk-border bg-kiosk-card hover:bg-kiosk-input text-kiosk-text',
        secondary: 'bg-kiosk-input text-kiosk-text hover:bg-kiosk-border',
        ghost: 'hover:bg-kiosk-input text-kiosk-text',
        link: 'text-kiosk-primary underline-offset-4 hover:underline',
        pin: 'bg-kiosk-card border-2 border-kiosk-border hover:border-kiosk-primary hover:bg-kiosk-input text-kiosk-text font-semibold text-lg h-14 w-14 rounded-xl transition-all duration-200',
        pinActive: 'bg-kiosk-primary border-2 border-kiosk-primary text-white font-semibold text-lg h-14 w-14 rounded-xl',
        primary: 'bg-kiosk-primary hover:opacity-90 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
        pin: 'h-14 w-14',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = 'Button';

export { Button, buttonVariants };
