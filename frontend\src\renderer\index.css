@import '@fontsource/inter';
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 1️⃣ Electron resets */
html, body, #root {
  @apply w-screen h-screen overflow-hidden;
}

/* 2️⃣ Viewport fix for kiosk */
body {
  margin: 0;
  padding: 0;
  -webkit-app-region: no-drag;   /* optional: allow drag on title-bar later */
  user-select: none;             /* kiosk mode */
}

/* 3️⃣ Tailwind helper for kiosk screens */
.kiosk {
  @apply w-screen h-screen bg-kiosk-bg text-kiosk-text;
}

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }



  /* 🎯 KIOSK UTILITY CLASSES - One-shot global system */
  .kiosk-input {
    @apply bg-kiosk-input border border-kiosk-border text-kiosk-text placeholder:text-kiosk-muted;
    @apply focus:outline-none focus:ring-2 focus:ring-kiosk-primary focus:border-kiosk-primary;
  }

  .kiosk-card {
    @apply bg-kiosk-card border border-kiosk-border text-kiosk-text;
  }

  .kiosk-button-primary {
    @apply bg-kiosk-primary text-white hover:opacity-90;
  }

  .kiosk-button-secondary {
    @apply bg-kiosk-input text-kiosk-text hover:bg-kiosk-border border border-kiosk-border;
  }

  .kiosk-text-primary {
    @apply text-kiosk-text;
  }

  .kiosk-text-muted {
    @apply text-kiosk-muted;
  }

  .kiosk-bg {
    @apply bg-kiosk-bg;
  }
}

@layer base {
  /* 🎯 KIOSK CSS VARIABLES - Global color system */
  :root {
    --kiosk-bg: #f9fafb;
    --kiosk-card: #ffffff;
    --kiosk-input: #f3f4f6;
    --kiosk-border: #e5e7eb;
    --kiosk-text: #111827;
    --kiosk-muted: #6b7280;
    --kiosk-primary: #3b82f6;
    --kiosk-error: #ef4444;
    --kiosk-success: #22c55e;
    --kiosk-warning: #f59e0b;
  }

  html, body {
    @apply bg-kiosk-bg text-kiosk-text font-sans;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Custom scrollbar for webkit browsers */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* Radix UI Dialog animations */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
  }

  @keyframes zoomIn {
    from {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
  }

  @keyframes zoomOut {
    from {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
    to {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.95);
    }
  }

  .animate-in {
    animation-duration: 200ms;
    animation-fill-mode: both;
  }

  .animate-out {
    animation-duration: 150ms;
    animation-fill-mode: both;
  }

  .fade-in-0 {
    animation-name: fadeIn;
  }

  .fade-out-0 {
    animation-name: fadeOut;
  }

  .zoom-in-95 {
    animation-name: zoomIn;
  }

  .zoom-out-95 {
    animation-name: zoomOut;
  }

  .slide-in-from-left-1\/2 {
    animation-name: zoomIn;
  }

  .slide-in-from-top-48 {
    animation-name: zoomIn;
  }

  .slide-out-to-left-1\/2 {
    animation-name: zoomOut;
  }

  .slide-out-to-top-48 {
    animation-name: zoomOut;
  }
}
