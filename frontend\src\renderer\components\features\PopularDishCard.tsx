// PopularDishCard.tsx
// Popüler yemekler için card component

import React from 'react';
import { Card, CardContent } from '../common/Card';
import { PopularDish } from '../../types/dashboard.types';
import { cn } from '../../lib/utils';

export interface PopularDishCardProps {
  dish: PopularDish;
  rank: number;
  className?: string;
}

export const PopularDishCard: React.FC<PopularDishCardProps> = ({
  dish,
  rank,
  className
}) => {
  return (
    <Card className={cn('backdrop-blur-sm border-0 shadow-md hover:shadow-xl transition-all duration-300', className)}>
      <CardContent className="p-3">
        <div className="flex items-center space-x-3">
          {/* Rank Badge */}
          <div className="flex-shrink-0">
            <div className={cn(
              'w-7 h-7 rounded-lg flex items-center justify-center text-xs font-bold shadow-md',
              rank === 1 && 'bg-gradient-to-br from-yellow-400 to-amber-500 text-white shadow-yellow-200/50',
              rank === 2 && 'bg-gradient-to-br from-slate-400 to-slate-500 text-white shadow-slate-200/50',
              rank === 3 && 'bg-gradient-to-br from-orange-400 to-red-500 text-white shadow-orange-200/50',
              rank > 3 && 'bg-gradient-to-br from-blue-400 to-indigo-500 text-white shadow-blue-200/50'
            )}>
              {rank}
            </div>
          </div>

          {/* Dish Image */}
          <div className="flex-shrink-0">
            <div className="relative">
              <img
                src={dish.image}
                alt={dish.name}
                className="w-10 h-10 rounded-lg object-cover shadow-sm"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/images/default-dish.jpg';
                }}
              />
              <div className="absolute inset-0 rounded-lg bg-gradient-to-t from-black/20 to-transparent"></div>
            </div>
          </div>

          {/* Dish Info */}
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-semibold text-kiosk-text truncate">
              {dish.name}
            </h4>
            <p className="text-xs text-kiosk-text font-medium">
              {dish.orders} sipariş
            </p>
          </div>

          {/* Order Count Badge */}
          <div className="flex-shrink-0">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-bold bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-md shadow-emerald-200/50">
              {dish.orders}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
