import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import * as Dialog from '@radix-ui/react-dialog';
import * as Select from '@radix-ui/react-select';
import { 
  X, 
  Save, 
  Loader2, 
  ChevronDown, 
  Check,
  FolderPlus
} from 'lucide-react';
import { Button } from '../common/Button';
import { useProductStore } from '../../store/productStore';
import { apiService } from '../../services/api.service';
import { 
  categoryFormSchema, 
  CategoryFormData,
  convertFormDataToCreateCategory
} from '../../lib/validations/product.validation';
import { cn } from '../../lib/utils';

interface CategoryFormProps {
  open: boolean;
  onClose: () => void;
}

export const CategoryForm: React.FC<CategoryFormProps> = ({ open, onClose }) => {
  const queryClient = useQueryClient();
  const {
    selectedCategory,
    categoryFormMode,
    addCategory,
    updateCategory,
    setError
  } = useProductStore();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm<CategoryFormData>({
    resolver: zodResolver(categoryFormSchema),
    defaultValues: {
      name: '',
      description: '',
      parentId: '',
      image: '',
      color: '',
      icon: '',
      displayOrder: '',
      printerGroupId: '',
      preparationTime: ''
    }
  });

  // Load parent categories for select
  const { data: categoriesData } = useQuery({
    queryKey: ['categories', { limit: 100 }],
    queryFn: () => apiService.getCategories({ limit: 100 }),
    staleTime: 10 * 60 * 1000 // 10 minutes
  });

  // Create mutation
  const createMutation = useMutation({
    mutationFn: (data) => {
      console.log('🔥 KATEGORI EKLEME DEBUG:');
      console.log('📦 apiService:', apiService);
      console.log('📦 Data gönderiliyor:', data);
      console.log('🔑 Auth token:', localStorage.getItem('auth_token'));
      console.log('👤 User data:', JSON.parse(localStorage.getItem('auth-storage') || '{}'));

      if (!apiService) {
        console.error('❌ apiService undefined!');
        throw new Error('apiService undefined');
      }

      if (!apiService.createCategory) {
        console.error('❌ apiService.createCategory undefined!');
        throw new Error('apiService.createCategory undefined');
      }

      return apiService.createCategory(data);
    },
    onSuccess: (response) => {
      console.log('✅ Kategori ekleme başarılı:', response);
      if (response.success) {
        addCategory(response.data);
        queryClient.invalidateQueries({ queryKey: ['categories'] });
        onClose();
        reset();
        // Show success toast
      }
    },
    onError: (error) => {
      console.error('❌ Kategori ekleme hatası:', error);
      console.error('❌ Error stack:', error.stack);
      setError({
        message: error instanceof Error ? error.message : 'Kategori oluşturulurken hata oluştu',
        type: 'create'
      });
    }
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => 
      apiService.updateCategory(id, data),
    onSuccess: (response) => {
      if (response.success) {
        updateCategory(response.data);
        queryClient.invalidateQueries({ queryKey: ['categories'] });
        onClose();
        reset();
        // Show success toast
      }
    },
    onError: (error) => {
      setError({
        message: error instanceof Error ? error.message : 'Kategori güncellenirken hata oluştu',
        type: 'update'
      });
    }
  });

  // Reset form when modal opens/closes or selected category changes
  React.useEffect(() => {
    if (open && selectedCategory && categoryFormMode === 'edit') {
      reset({
        name: selectedCategory.name,
        description: selectedCategory.description || '',
        parentId: selectedCategory.parentId || '',
        image: selectedCategory.image || '',
        color: selectedCategory.color || '',
        icon: selectedCategory.icon || '',
        displayOrder: selectedCategory.displayOrder.toString(),
        printerGroupId: selectedCategory.printerGroupId || '',
        preparationTime: selectedCategory.preparationTime?.toString() || ''
      });
    } else if (open && categoryFormMode === 'create') {
      reset({
        name: '',
        description: '',
        parentId: '',
        image: '',
        color: '',
        icon: '',
        displayOrder: '',
        printerGroupId: '',
        preparationTime: ''
      });
    }
  }, [open, selectedCategory, categoryFormMode, reset]);

  const onSubmit = (data: CategoryFormData) => {
    console.log('🚀 FORM SUBMIT DEBUG:');
    console.log('📝 Form data:', data);

    const categoryData = convertFormDataToCreateCategory(data);
    console.log('🔄 Converted data:', categoryData);
    console.log('🎯 Form mode:', categoryFormMode);

    if (categoryFormMode === 'create') {
      console.log('➕ CREATE modunda, mutation çağrılıyor...');
      createMutation.mutate(categoryData);
    } else if (selectedCategory) {
      console.log('✏️ UPDATE modunda, mutation çağrılıyor...');
      updateMutation.mutate({
        id: selectedCategory.id,
        data: categoryData
      });
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <Dialog.Root open={open} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-50" />
        <Dialog.Content
          className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-kiosk-card rounded-lg shadow-xl z-50 w-full max-w-lg max-h-[90vh] overflow-y-auto"
          aria-describedby="category-form-desc"
        >
          <div className="p-6">
            <span id="category-form-desc" className="sr-only">Kategori ekleme ve düzenleme formu</span>
            <div className="flex items-center justify-between mb-6">
              <div>
                <Dialog.Title className="text-lg font-semibold text-kiosk-text">
                  {categoryFormMode === 'create' ? 'Yeni Kategori Ekle' : 'Kategori Düzenle'}
                </Dialog.Title>
                <Dialog.Description className="text-sm text-kiosk-muted mt-1">
                  {categoryFormMode === 'create'
                    ? 'Yeni bir kategori oluşturun ve ürünlerinizi organize edin.'
                    : 'Kategori bilgilerini güncelleyin.'
                  }
                </Dialog.Description>
              </div>
              <Dialog.Close asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <X className="w-4 h-4" />
                </Button>
              </Dialog.Close>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {/* Category Name */}
              <div>
                <label className="block text-sm font-medium text-kiosk-text mb-1">
                  Kategori Adı *
                </label>
                <input
                  {...register('name')}
                  type="text"
                  className={cn(
                    "w-full px-3 py-2 border rounded-md text-sm bg-kiosk-input",
                    "focus:outline-none focus:ring-2 focus:ring-kiosk-primary focus:border-transparent",
                    errors.name ? "border-kiosk-error" : "border-kiosk-border"
                  )}
                  placeholder="Kategori adını giriniz"
                />
                {errors.name && (
                  <p className="mt-1 text-xs text-kiosk-text">{errors.name.message}</p>
                )}
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-kiosk-text mb-1">
                  Açıklama
                </label>
                <textarea
                  {...register('description')}
                  rows={3}
                  className={cn(
                    "w-full px-3 py-2 border rounded-md text-sm resize-none bg-kiosk-input",
                    "focus:outline-none focus:ring-2 focus:ring-kiosk-primary focus:border-transparent",
                    errors.description ? "border-kiosk-error" : "border-kiosk-border"
                  )}
                  placeholder="Kategori açıklaması (isteğe bağlı)"
                />
                {errors.description && (
                  <p className="mt-1 text-xs text-kiosk-text">{errors.description.message}</p>
                )}
              </div>

              {/* Parent Category */}
              <div>
                <label className="block text-sm font-medium text-kiosk-text mb-1">
                  Üst Kategori
                </label>
                <Select.Root
                  value={watch('parentId')}
                  onValueChange={(value) => setValue('parentId', value === 'none' ? '' : value)}
                >
                  <Select.Trigger className={cn(
                    "w-full px-3 py-2 border rounded-md text-sm bg-kiosk-input",
                    "focus:outline-none focus:ring-2 focus:ring-kiosk-primary focus:border-transparent",
                    "flex items-center justify-between",
                    errors.parentId ? "border-kiosk-error" : "border-kiosk-border"
                  )}>
                    <Select.Value placeholder="Üst kategori seçiniz (isteğe bağlı)" />
                    <Select.Icon>
                      <ChevronDown className="w-4 h-4" />
                    </Select.Icon>
                  </Select.Trigger>
                  <Select.Portal>
                    <Select.Content className="bg-kiosk-input border border-kiosk-border rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
                      <Select.Viewport className="p-1">
                        <Select.Item
                          value="none"
                          className="px-3 py-2 text-sm cursor-pointer hover:bg-kiosk-input rounded flex items-center justify-between"
                        >
                          <Select.ItemText>Ana Kategori</Select.ItemText>
                          <Select.ItemIndicator>
                            <Check className="w-4 h-4" />
                          </Select.ItemIndicator>
                        </Select.Item>
                        {categoriesData?.data?.map((category) => (
                          <Select.Item
                            key={category.id}
                            value={category.id}
                            className="px-3 py-2 text-sm cursor-pointer hover:bg-kiosk-input rounded flex items-center justify-between"
                          >
                            <div className="flex items-center gap-2">
                              <div 
                                className="w-3 h-3 rounded-full"
                                style={{ backgroundColor: category.color || '#6B7280' }}
                              />
                              <Select.ItemText>{category.name}</Select.ItemText>
                            </div>
                            <Select.ItemIndicator>
                              <Check className="w-4 h-4" />
                            </Select.ItemIndicator>
                          </Select.Item>
                        ))}
                      </Select.Viewport>
                    </Select.Content>
                  </Select.Portal>
                </Select.Root>
                {errors.parentId && (
                  <p className="mt-1 text-xs text-kiosk-text">{errors.parentId.message}</p>
                )}
              </div>

              {/* Color */}
              <div>
                <label className="block text-sm font-medium text-kiosk-text mb-1">
                  Renk
                </label>
                <input
                  {...register('color')}
                  type="color"
                  className={cn(
                    "w-full h-10 border rounded-md",
                    "focus:outline-none focus:ring-2 focus:ring-kiosk-primary focus:border-transparent",
                    errors.color ? "border-kiosk-error" : "border-kiosk-border"
                  )}
                />
                {errors.color && (
                  <p className="mt-1 text-xs text-kiosk-text">{errors.color.message}</p>
                )}
              </div>

              {/* Image URL */}
              <div>
                <label className="block text-sm font-medium text-kiosk-text mb-1">
                  Kategori Görseli (URL)
                </label>
                <input
                  {...register('image')}
                  type="url"
                  className={cn(
                    "w-full px-3 py-2 border rounded-md text-sm bg-kiosk-input",
                    "focus:outline-none focus:ring-2 focus:ring-kiosk-primary focus:border-transparent",
                    errors.image ? "border-kiosk-error" : "border-kiosk-border"
                  )}
                  placeholder="https://example.com/image.jpg"
                />
                {errors.image && (
                  <p className="mt-1 text-xs text-kiosk-text">{errors.image.message}</p>
                )}
              </div>

              {/* Form Actions */}
              <div className="flex items-center justify-end gap-3 pt-6 border-t border-kiosk-border">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={isLoading}
                >
                  İptal
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="min-w-[100px]"
                >
                  {isLoading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      {categoryFormMode === 'create' ? 'Oluştur' : 'Güncelle'}
                    </>
                  )}
                </Button>
              </div>
            </form>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};
