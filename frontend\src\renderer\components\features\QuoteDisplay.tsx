import React, { useState, useEffect } from 'react';
import { Quote } from 'lucide-react';

const motivationalQuotes = [
  {
    text: "<PERSON><PERSON><PERSON><PERSON>, hazırlık ile fırsatın buluştuğu and<PERSON>r.",
    author: "<PERSON>"
  },
  {
    text: "Müşteri memnuniyeti bizim en büyük önceliğimizdir.",
    author: "Atropos Restaurant"
  },
  {
    text: "Her gün yeni bir başlangıçtır.",
    author: "T.S<PERSON> Eliot"
  },
  {
    text: "<PERSON><PERSON> as<PERSON>, her zaman akıllı çabanın sonucudur.",
    author: "<PERSON>"
  },
  {
    text: "<PERSON><PERSON><PERSON><PERSON> çal<PERSON>şmas<PERSON>, sıradan insanların olağanüstü sonuçlar elde etmesini sağlar.",
    author: "<PERSON>"
  },
  {
    text: "Mükemmellik bir alışkanlıktır, tek seferlik bir eylem değil.",
    author: "Aristoteles"
  }
];

export const QuoteDisplay: React.FC = () => {
  const [currentQuote, setCurrentQuote] = useState(motivationalQuotes[0]);

  useEffect(() => {
    const interval = setInterval(() => {
      const randomIndex = Math.floor(Math.random() * motivationalQuotes.length);
      setCurrentQuote(motivationalQuotes[randomIndex]);
    }, 10000); // Her 10 saniyede bir değişir

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="bg-gradient-to-br from-blue-50 to-indigo-100 p-6 rounded-xl border border-blue-200">
      <div className="flex items-start space-x-4">
        <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
          <Quote className="w-5 h-5 text-white" />
        </div>
        <div className="flex-1">
          <blockquote className="text-kiosk-text text-lg font-medium leading-relaxed mb-3">
            "{currentQuote.text}"
          </blockquote>
          <cite className="text-kiosk-primary font-semibold text-sm">
            — {currentQuote.author}
          </cite>
        </div>
      </div>
    </div>
  );
};
